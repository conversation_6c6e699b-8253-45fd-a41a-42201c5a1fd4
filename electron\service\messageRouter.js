"use strict";

const { logger } = require("ee-core/log");

/**
 * 消息路由器
 * 定义不同消息ID的发送策略
 */
class MessageRouter {
  static instance = null;

  static getInstance() {
    if (!MessageRouter.instance) {
      MessageRouter.instance = new MessageRouter();
    }
    return MessageRouter.instance;
  }

  constructor() {
    if (MessageRouter.instance) {
      return MessageRouter.instance;
    }

    // 消息路由规则
    this.routingRules = {
      // 同时发送到WebSocket和USB的消息
      dual: [1100, 2048],
      
      // 仅发送到WebSocket的消息
      websocketOnly: [20, 11, 12],
      
      // 仅发送到USB的消息
      usbOnly: []
    };

    MessageRouter.instance = this;
  }

  /**
   * 获取消息的路由目标
   * @param {number} messageId - 消息ID
   * @returns {string} 路由目标 ('dual', 'websocket', 'usb')
   */
  getRouteTarget(messageId) {
    if (this.routingRules.dual.includes(messageId)) {
      return 'all';
    }
    
    if (this.routingRules.websocketOnly.includes(messageId)) {
      return 'websocket';
    }
    
    if (this.routingRules.usbOnly.includes(messageId)) {
      return 'usb';
    }

    // 默认只发送到WebSocket
    logger.warn(`[MessageRouter] Unknown message ID ${messageId}, defaulting to websocket only`);
    return 'websocket';
  }

  /**
   * 检查消息是否应该发送到指定连接
   * @param {number} messageId - 消息ID
   * @param {string} connectionType - 连接类型 ('websocket' 或 'usb')
   * @returns {boolean}
   */
  shouldSendTo(messageId, connectionType) {
    const target = this.getRouteTarget(messageId);
    
    if (target === 'all') {
      return true;
    }
    
    return target === connectionType;
  }

  /**
   * 添加路由规则
   * @param {number} messageId - 消息ID
   * @param {string} target - 目标 ('dual', 'websocket', 'usb')
   */
  addRule(messageId, target) {
    // 先从所有规则中移除
    this.removeRule(messageId);
    
    switch (target) {
      case 'dual':
      case 'all':
        this.routingRules.dual.push(messageId);
        break;
      case 'websocket':
        this.routingRules.websocketOnly.push(messageId);
        break;
      case 'usb':
        this.routingRules.usbOnly.push(messageId);
        break;
      default:
        logger.warn(`[MessageRouter] Invalid target ${target} for message ID ${messageId}`);
        break;
    }
    
    logger.info(`[MessageRouter] Added rule: Message ID ${messageId} -> ${target}`);
  }

  /**
   * 移除路由规则
   * @param {number} messageId - 消息ID
   */
  removeRule(messageId) {
    this.routingRules.dual = this.routingRules.dual.filter(id => id !== messageId);
    this.routingRules.websocketOnly = this.routingRules.websocketOnly.filter(id => id !== messageId);
    this.routingRules.usbOnly = this.routingRules.usbOnly.filter(id => id !== messageId);
  }

  /**
   * 获取所有路由规则
   */
  getAllRules() {
    return {
      dual: [...this.routingRules.dual],
      websocketOnly: [...this.routingRules.websocketOnly],
      usbOnly: [...this.routingRules.usbOnly]
    };
  }

  /**
   * 重置为默认规则
   */
  resetToDefault() {
    this.routingRules = {
      dual: [1100, 2048],
      websocketOnly: [20, 11, 12],
      usbOnly: []
    };
    
    logger.info("[MessageRouter] Reset to default routing rules");
  }

  /**
   * 从配置加载路由规则
   * @param {object} config - 配置对象
   */
  loadFromConfig(config) {
    if (config && typeof config === 'object') {
      this.routingRules = {
        dual: config.dual || [],
        websocketOnly: config.websocketOnly || [],
        usbOnly: config.usbOnly || []
      };
      
      logger.info("[MessageRouter] Loaded routing rules from config");
    }
  }

  /**
   * 导出当前配置
   */
  exportConfig() {
    return {
      dual: [...this.routingRules.dual],
      websocketOnly: [...this.routingRules.websocketOnly],
      usbOnly: [...this.routingRules.usbOnly]
    };
  }

  /**
   * 获取路由统计信息
   */
  getStats() {
    return {
      totalRules: this.routingRules.dual.length + 
                  this.routingRules.websocketOnly.length + 
                  this.routingRules.usbOnly.length,
      dualCount: this.routingRules.dual.length,
      websocketOnlyCount: this.routingRules.websocketOnly.length,
      usbOnlyCount: this.routingRules.usbOnly.length,
      rules: this.getAllRules()
    };
  }

  /**
   * 验证消息ID是否有效
   * @param {number} messageId - 消息ID
   * @returns {boolean}
   */
  isValidMessageId(messageId) {
    return typeof messageId === 'number' && messageId > 0 && messageId <= 65535;
  }

  /**
   * 批量添加规则
   * @param {Array} rules - 规则数组 [{messageId: number, target: string}]
   */
  addBatchRules(rules) {
    if (!Array.isArray(rules)) {
      logger.error("[MessageRouter] Rules must be an array");
      return;
    }

    let addedCount = 0;
    for (const rule of rules) {
      if (rule.messageId && rule.target && this.isValidMessageId(rule.messageId)) {
        this.addRule(rule.messageId, rule.target);
        addedCount++;
      } else {
        logger.warn(`[MessageRouter] Invalid rule:`, rule);
      }
    }

    logger.info(`[MessageRouter] Added ${addedCount} rules in batch`);
  }

  /**
   * 获取消息ID的详细路由信息
   * @param {number} messageId - 消息ID
   */
  getMessageRouteInfo(messageId) {
    const target = this.getRouteTarget(messageId);
    
    return {
      messageId,
      target,
      sendToWebSocket: this.shouldSendTo(messageId, 'websocket'),
      sendToUSB: this.shouldSendTo(messageId, 'usb'),
      isDual: target === 'all'
    };
  }
}

module.exports = MessageRouter.getInstance();
