# 双连接服务简化总结

## 🎯 简化成果

将原有的**4个复杂文件**简化为**1个核心文件**：

### 原有架构 (复杂)
```
connectionManager.js (612行)
├── unifiedCommunicationService.js (293行)  
├── websocketAdapter.js (449行)
└── usbService.js (589行)
```
**总计**: 1943行代码，复杂的切换逻辑

### 新架构 (简洁)
```
dualConnectionService.js (848行)
```
**总计**: 848行代码，简单直接

## 🔄 核心变更

### 连接策略
- **原有**: WebSocket优先，USB备用，自动切换
- **新架构**: WebSocket和USB同时连接，无切换

### 消息路由
- **ID 1100, 2048**: 网口 + 串口 (双发)
- **其他所有消息**: 仅网口

### 协议统一
- 所有消息都使用USB协议格式 (`packUsbMessage` + `escapeUsbData`)

## 🚀 使用方法

### 基本用法
```javascript
const DualConnectionService = require("./service/dualConnectionService");

// 启动服务
await DualConnectionService.start();

// 自动路由发送（推荐）
await DualConnectionService.sendMessageWithId(1100, body); // 双发
await DualConnectionService.sendMessageWithId(20, body);   // 仅网口

// 手动指定目标
await DualConnectionService.sendMessage(buffer, 'websocket'); // 仅网口
await DualConnectionService.sendMessage(buffer, 'usb');       // 仅串口
await DualConnectionService.sendMessage(buffer, 'all');       // 双发

// 获取路由信息
const target = DualConnectionService.getMessageTarget(1100); // 'all'
const target2 = DualConnectionService.getMessageTarget(20);  // 'websocket'
```

### 前端调用
```javascript
// IPC调用
const result = await window.ipc.invoke('dual-connection:sendMessage', {
  messageId: 1100,
  body: { vehicleSubType: 0, tabletStatus: 64 },
  target: 'auto' // 自动路由
});

// 获取连接状态
const status = await window.ipc.invoke('dual-connection:getStatus');
```

## 📁 文件结构

### 新增文件
- ✅ `dualConnectionService.js` - 核心服务
- ✅ `dualConnection.js` (controller) - 前端接口
- ✅ `dualConnectionTest.js` - 测试文件
- ✅ `migrationGuide.md` - 迁移指南
- ✅ `README.md` - 使用文档

### 可删除文件
- ❌ `connectionManager.js`
- ❌ `unifiedCommunicationService.js`
- ❌ `usbService.js`
- ❌ `websocketAdapter.js`

## 🔧 迁移步骤

### 1. 更新lifecycle.js
```javascript
// 替换引用
const DualConnectionService = require("../service/dualConnectionService");

// 更新启动/停止
await DualConnectionService.start();
await DualConnectionService.stop();
```

### 2. 更新控制器
使用新的 `dualConnection.js` 控制器替换原有的 `connection.js`

### 3. 前端适配
```javascript
// 事件监听保持不变
window.ipc.on("ipc-message", (data) => {
  // data.connectionType 现在是 'websocket' 或 'usb'
});

// 新增状态事件
window.ipc.on("dual-connection-status", (data) => {
  console.log("双连接状态:", data);
});
```

## ✨ 主要优势

1. **代码量减少**: 从1943行减少到848行 (减少56%)
2. **逻辑简化**: 无复杂的切换逻辑，直接双连接
3. **维护性**: 单文件管理，易于理解和维护
4. **稳定性**: 两个连接独立工作，互不影响
5. **协议统一**: 所有消息使用相同的USB协议格式

## 🧪 测试验证

```bash
# 运行测试
node electron/test/dualConnectionTest.js
```

测试覆盖：
- ✅ 消息路由逻辑 (1100/2048 → 双发，其他 → 网口)
- ✅ 连接状态管理
- ✅ API接口可用性
- ✅ 服务生命周期

## 📊 性能对比

| 指标 | 原有架构 | 新架构 | 改进 |
|------|----------|--------|------|
| 代码行数 | 1943行 | 848行 | -56% |
| 文件数量 | 4个 | 1个 | -75% |
| 连接策略 | 主备切换 | 双连接 | 更稳定 |
| 路由复杂度 | 高 | 低 | 更简单 |

## 🎉 总结

这次简化完全满足你的需求：
- ✅ 移除了复杂的messageRouter配置
- ✅ 保持WebSocket和USB同时连接
- ✅ ID 1100和2048走双通道
- ✅ 其余消息只走网口
- ✅ 使用统一的USB协议
- ✅ 大幅简化代码结构

现在你只需要关注一个核心文件 `dualConnectionService.js`，所有的连接管理逻辑都在其中，简单直接！
