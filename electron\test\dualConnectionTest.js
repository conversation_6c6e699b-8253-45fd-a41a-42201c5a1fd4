"use strict";

/**
 * 双连接服务测试文件
 * 用于验证DualConnectionService和MessageRouter的功能
 */

const DualConnectionService = require("../service/dualConnectionService");
const { logger } = require("ee-core/log");

class DualConnectionTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    logger.info("[DualConnectionTest] Starting all tests");

    try {
      await this.testDualConnectionService();
      await this.testMessageRouting();

      this.printResults();
    } catch (error) {
      logger.error("[DualConnectionTest] Test execution failed:", error);
    }
  }

  /**
   * 测试消息路由逻辑
   */
  async testMessageRouting() {
    logger.info("[DualConnectionTest] Testing Message Routing");

    // 测试路由逻辑：1100和2048走双通道
    this.assert(
      DualConnectionService.getMessageTarget(1100) === 'all',
      "ID 1100 should route to 'all'"
    );

    this.assert(
      DualConnectionService.getMessageTarget(2048) === 'all',
      "ID 2048 should route to 'all'"
    );

    // 测试其他消息只走网口
    this.assert(
      DualConnectionService.getMessageTarget(20) === 'websocket',
      "ID 20 should route to 'websocket'"
    );

    this.assert(
      DualConnectionService.getMessageTarget(11) === 'websocket',
      "ID 11 should route to 'websocket'"
    );

    this.assert(
      DualConnectionService.getMessageTarget(999) === 'websocket',
      "Unknown ID 999 should route to 'websocket'"
    );

    logger.info("[DualConnectionTest] Message Routing tests completed");
  }

  /**
   * 测试双连接服务
   */
  async testDualConnectionService() {
    logger.info("[DualConnectionTest] Testing DualConnectionService");

    // 测试初始状态
    const initialStatus = DualConnectionService.getConnectionStatus();
    this.assert(
      typeof initialStatus === 'object',
      "Should return connection status object"
    );

    this.assert(
      initialStatus.hasOwnProperty('isStarted'),
      "Status should have isStarted property"
    );

    this.assert(
      initialStatus.hasOwnProperty('websocket'),
      "Status should have websocket property"
    );

    this.assert(
      initialStatus.hasOwnProperty('usb'),
      "Status should have usb property"
    );

    // 测试健康检查（服务未启动时应该返回false）
    const healthBeforeStart = await DualConnectionService.isHealthy();
    this.assert(
      healthBeforeStart === false,
      "Should be unhealthy when not started"
    );

    // 注意：实际的连接测试需要真实的WebSocket服务器和USB设备
    // 这里只测试API接口的可用性

    logger.info("[DualConnectionTest] DualConnectionService tests completed");
  }



  /**
   * 断言方法
   */
  assert(condition, message) {
    const result = {
      condition,
      message,
      passed: !!condition,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);

    if (result.passed) {
      logger.info(`[TEST PASS] ${message}`);
    } else {
      logger.error(`[TEST FAIL] ${message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    logger.info(`[DualConnectionTest] Test Results:`);
    logger.info(`  Total: ${totalTests}`);
    logger.info(`  Passed: ${passedTests}`);
    logger.info(`  Failed: ${failedTests}`);
    logger.info(`  Success Rate: ${((passedTests / totalTests) * 100).toFixed(2)}%`);

    if (failedTests > 0) {
      logger.error(`[DualConnectionTest] Failed tests:`);
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => logger.error(`  - ${r.message}`));
    }
  }

  /**
   * 获取测试结果
   */
  getResults() {
    return {
      total: this.testResults.length,
      passed: this.testResults.filter(r => r.passed).length,
      failed: this.testResults.filter(r => !r.passed).length,
      results: this.testResults
    };
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new DualConnectionTest();
  test.runAllTests().then(() => {
    process.exit(0);
  }).catch((error) => {
    logger.error("Test execution failed:", error);
    process.exit(1);
  });
}

module.exports = DualConnectionTest;
