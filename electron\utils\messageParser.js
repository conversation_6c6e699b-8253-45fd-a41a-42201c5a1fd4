"use strict";

const { calculateCrc16 } = require("./crc");

/**
 * 消息解析工具类
 * @class
 *
 * @example WebSocket消息格式
 * 0x0C 0x10 0x22 0x04 0x14 0x00 0x81 0x02 0x00 0x01 0x50 0x30 0xFC 0xAF 0x7F 0x60 0x95 0x01 0x00 0x00
 *
 * @example MQTT消息格式
 * // Buffer输出
 * <Buffer 0c 10 22 04 14 00 81 02 00 01 50 30 fc af 7f 60 95 01 00 00>
 *
 * // Buffer.toString("hex")字符串输出
 * 0c10220114008102a5014400ac8b3064950100000800440001010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001100000000000080000000000000000
 */
class MessageParser {
  static _usbBuffer = new Uint8Array(0);

  /**
   * 解析消息
   * @param {string} message - 消息内容
   * @returns {object} 解析后的消息对象
   */

  static parse(message, type = "mqtt") {
    try {
      if (type === "mqtt") {
        const data8 = MessageParser.Str2Uint8Array(message.toString("hex"));
        return MessageParser.parseData8(data8);
      } else if (type === "websocket") {
        const cleanHex = message
          .replace(/0x|\n/g, "") // 删除 0x 和换行符
          .replace(/\s+/g, "") // 删除所有空格
          .trim(); // 去除首尾空白
        const data8 = MessageParser.Str2Uint8Array(cleanHex);
        console.log("data8", data8);
        return MessageParser.parseData8(data8);
      } else if (type === "usb") {
        return MessageParser.parseUsbMessage(message);
      }
    } catch (error) {
      console.error("Failed to parse message:", error);
      return null;
    }
  }

  /**
   * 将字符串转换为 Uint8Array
   * @param {string} value - 要转换的字符串
   * @returns {Uint8Array} 转换后的 Uint8Array
   */
  static Str2Uint8Array(value) {
    let str = value.replace(/\s*/g, ""); // 先去除前后空格

    if (str.length === 0) return new Uint8Array(0); // 直接返回空数组
    if (str.length % 2 !== 0) str = "0" + str; // 确保长度为偶数

    const len = str.length / 2;

    if (len < 20) return new Uint8Array(0); // 直接返回空数组

    const data8 = new Uint8Array(len); // 直接分配 Uint8Array

    for (let i = 0; i < len; i++) {
      data8[i] = parseInt(str.substr(i * 2, 2), 16);
    }

    return data8;
  }

  static parseData8(data) {
    const { parseConfig } = require("./messageDict");

    if (!data || data.length < 20) {
      console.error("Invalid data length");
      return null;
    }

    let res = {};
    let flag = 0;

    const dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);

    // 解析头部信息
    try {
      res = MessageParser.parseHeader(dataView, parseConfig.head);
      flag = MessageParser.calculateHeaderLength(parseConfig.head);
    } catch (error) {
      console.error("Error parsing header:", error);
      return null;
    }

    // 如果只有头部信息，直接返回
    if (data.byteLength === 20) return res;

    // 解析消息体
    try {
      const bodyOffset = res["headerLength"];
      const id_ = dataView.getUint16(bodyOffset || 0, true);
      const config = parseConfig[`ID${id_}`];

      if (!config) {
        // 未知消息ID
        // console.warn(`Unknown message ID: ${id_}`);
        return res;
      }

      res = {
        ...res,
        ...MessageParser.parseMessageBody(dataView, config, id_, flag),
      };
    } catch (error) {
      console.error("Error parsing message body:", error);
      return res;
    }

    return res;
  }

  static parseHeader(dataView, headerConfig) {
    const res = {};
    let flag = 0;

    for (const item of headerConfig) {
      try {
        res[item.key] = MessageParser.parseValue(dataView, item, flag);
        if (item.key === "id") console.log("parseHeader ID", res[item.key]);
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing header field ${item.key}:`, error);
        throw error;
      }
    }

    return res;
  }

  static parseMessageBody(dataView, config, id_, startFlag) {
    const res = {};
    let flag = startFlag;

    // 通用的消息体解析
    for (const item of config) {
      try {
        if (id_ === 11 && item.key === "otherInfo") {
          res[item.key] = MessageParser.parseStatusInfo(dataView, flag, res["senderStatus"]);
        } else if (id_ === 20 && item.key === "jsonData") {
          res[item.key] = MessageParser.parseJsonData(dataView, flag);
        } else {
          res[item.key] = MessageParser.parseValue(dataView, item, flag);
        }
        flag += item.len;
      } catch (error) {
        console.error(`Error parsing body field ${item.key}:`, error);
        continue;
      }
    }

    return res;
  }

  static parseValue(dataView, item, offset) {
    switch (item.len) {
      case 0:
        return "";
      case 1:
        return dataView.getUint8(offset);
      case 2:
        // 特征位特殊处理
        if (item.key === "featureValue") {
          return String.fromCharCode(dataView.getUint8(0)) + String.fromCharCode(dataView.getUint8(1));
        }
        return dataView.getUint16(offset, true);
      case 4:
        if (item.type === "FLOAT") {
          return dataView.getFloat32(offset, true);
        } else if (item.type === "BIN") {
          return dataView.getUint32(offset, true);
        }
        return dataView.getUint32(offset, true);
      case 8:
        if (item.type === "TIME") {
          const timestamp = dataView.getBigUint64(offset, true);
          // return MessageParser.formatTimestamp(parseInt(timestamp.toString()));
          return parseInt(timestamp.toString());
        }
        return `${dataView.getUint16(offset, true)},${dataView.getUint16(
          offset + 2,
          true
        )},${dataView.getUint16(offset + 4, true)},${dataView.getUint16(offset + 6, true)}`;
      case 24:
        if (item.key === "extendedGeneralFeatures") {
          const buttons = [];
          // 解析前3字节的12个按钮状态
          for (let i = 0; i < 3; i++) {
            const byte = dataView.getUint8(offset + i);
            for (let j = 0; j < 4; j++) {
              buttons.push((byte >> (j * 2)) & 0x03);
            }
          }

          // 获取触摸点数量
          const touchCount = dataView.getUint8(offset + 3);

          // 解析5组触摸坐标
          const touchPoints = [];
          for (let i = 0; i < 5; i++) {
            const x = dataView.getUint16(offset + 4 + i * 4, true);
            const y = dataView.getUint16(offset + 4 + i * 4 + 2, true);
            touchPoints.push({ x, y });
          }

          return {
            buttons,
            touchCount,
            touchPoints,
          };
        }
        return null;
      default:
        return null;
    }
  }

  static parseStatusInfo(dataView, offset, senderStatus) {
    const statusInfo = {};
    const byte1 = dataView.getUint8(offset);
    const byte2 = dataView.getUint8(offset + 1);
    const byte3 = dataView.getUint8(offset + 2);

    switch (senderStatus) {
      case 8: // 初始化
        statusInfo.parameterStatus = !!(byte1 & 0x01);
        statusInfo.modeType = byte1 & 0x02 ? "远程模式" : "本地模式";
        statusInfo.moduleStatus = MessageParser.parseModuleStatus(byte1, byte2);
        break;
      case 32: // 等待
        statusInfo.vehicleTypeSpecified = !!(byte1 & 0x80);
        break;
      case 48: // 登录
        statusInfo.subVehicleType = (byte2 << 8) | byte1;
        break;
      case 64: // 远控
      case 80: // 锁定
      case 88: // 失效保护
        statusInfo.serialInfo = MessageParser.parseSerialInfo(byte1, byte2, byte3);
        break;
    }

    return statusInfo;
  }

  static parseJsonData(dataView, offset) {
    const dataViewOffset = dataView.buffer.slice(offset);
    const decoder = new TextDecoder("utf-8");
    const jsonString = decoder.decode(dataViewOffset);
    return JSON.parse(jsonString);
  }

  static parseModuleStatus(byte1, byte2) {
    const getModuleStatus = (bits) => {
      const statusMap = {
        0: "未连接",
        1: "已连接未装订参数",
        2: "已连接已装订参数",
        3: "已连接已完成初始化",
      };
      return statusMap[bits] || "未知状态";
    };

    return {
      module1: getModuleStatus((byte1 >> 4) & 0x03),
      module2: getModuleStatus((byte1 >> 6) & 0x03),
      module3: getModuleStatus(byte2 & 0x03),
      module4: getModuleStatus((byte2 >> 2) & 0x03),
    };
  }

  static parseSerialInfo(byte1, byte2, byte3) {
    return {
      year: byte1,
      month: byte2,
      number: byte3,
      extended: {
        month: (byte2 >> 4) & 0x0f,
        number: ((byte2 & 0x0f) << 8) | byte3,
      },
    };
  }

  static calculateHeaderLength(headerConfig) {
    return headerConfig.reduce((sum, item) => sum + item.len, 0);
  }

  static formatTimestamp(timestamp) {
    // 检查时间戳是否为秒级，如果是，则转换为毫秒
    if (timestamp.toString().length === 10) {
      timestamp *= 1000;
    }

    const date = new Date(timestamp);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0"); // 毫秒部分

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * USB消息解析主方法
   * @param {Buffer|Uint8Array} rawData - 原始USB数据
   * @returns {object|null} 解析后的消息对象
   */
  static parseUsbMessage(rawData) {
    try {
      // 转换为Uint8Array
      let data;
      if (Buffer.isBuffer(rawData)) {
        console.log("rawData is Buffer", rawData);
        data = new Uint8Array(rawData);
      } else {
        console.error("Unsupported USB data format");
        return null;
      }

      const combinedData = new Uint8Array(this._usbBuffer.length + data.length);
      combinedData.set(this._usbBuffer, 0);
      combinedData.set(data, this._usbBuffer.length);

      // 查找完整的消息包
      const { packets, remainingData } = MessageParser.extractUsbPackets(combinedData);
      this._usbBuffer = remainingData;

      if (packets.length === 0) {
        console.log("No complete USB packets found");
        return null;
      }

      // 解析第一个完整的包（如果有多个包，只处理第一个）
      const packet = packets[0];
      console.log("Processing USB packet, length:", packet.length);

      // 验证和解包
      const unpackedData = MessageParser.unpackUsbMessage(packet);
      if (!unpackedData) {
        console.error("Failed to unpack USB message");
        return null;
      }

      // 使用现有的parseData8方法解析消息内容
      return MessageParser.parseData8(unpackedData);
    } catch (error) {
      console.error("Failed to parse USB message:", error);
      return null;
    }
  }

  /**
   * 从USB数据流中提取完整的消息包
   * @param {Uint8Array} data - USB数据流
   * @returns {Array<Uint8Array>} 完整的消息包数组
   */
  static extractUsbPackets(data) {
    const packets = [];
    const STX = 0x7e; // 起始符
    const ETX = 0x7d; // 终止符
    let searchOffset = 0;

    while (searchOffset < data.length) {
      // 查找起始符
      const startIndex = data.indexOf(STX, searchOffset);
      if (startIndex === -1) {
        break;
      }

      // 查找对应的终止符
      const endIndex = data.indexOf(ETX, startIndex + 1);
      if (endIndex === -1) {
        searchOffset = startIndex;
        break;
      }

      // 提取完整的包（包括起始符和终止符）
      const packetLength = endIndex - startIndex + 1;
      const packet = data.slice(startIndex, endIndex + 1);
      packets.push(packet);

      console.log(`Found USB packet: start=${startIndex}, end=${endIndex}, length=${packetLength}`);

      // 继续查找下一个包
      searchOffset = endIndex + 1;
    }

    const remainingData = data.slice(searchOffset);
    return { packets, remainingData };
  }

  /**
   * 解包USB消息（反转义 + CRC校验 + 移除封装）
   * @param {Uint8Array} packet - 完整的USB消息包
   * @returns {Uint8Array|null} 解包后的原始消息数据
   */
  static unpackUsbMessage(packet) {
    try {
      const STX = 0x7e;
      const ETX = 0x7d;

      // 验证包格式
      if (packet.length < 4 || packet[0] !== STX || packet[packet.length - 1] !== ETX) {
        console.error("Invalid USB packet format");
        return null;
      }

      // 提取封装内的数据（去除STX和ETX）
      const encapsulatedData = packet.slice(1, -1);

      // 反转义数据
      const unescapedData = MessageParser.unescapeUsbData(encapsulatedData);

      // 验证最小长度（至少要有2字节CRC）
      if (unescapedData.length < 2) {
        console.error("USB packet too short for CRC");
        return null;
      }

      // 分离数据和CRC
      const messageData = unescapedData.slice(0, -2);
      const receivedCrc = (unescapedData[unescapedData.length - 1] << 8) | unescapedData[unescapedData.length - 2];

      // 计算CRC校验
      const calculatedCrc = calculateCrc16(messageData);

      console.log(`CRC check: received=0x${receivedCrc.toString(16)}, calculated=0x${calculatedCrc.toString(16)}`);

      if (receivedCrc !== calculatedCrc) {
        console.error("USB message CRC check failed");
        return null;
      }

      console.log("USB message unpacked successfully, data length:", messageData.length);
      return messageData;
    } catch (error) {
      console.error("Error unpacking USB message:", error);
      return null;
    }
  }

  /**
   * USB数据反转义
   * @param {Uint8Array} data - 转义后的数据
   * @returns {Uint8Array} 反转义后的数据
   */
  static unescapeUsbData(data) {
    const result = [];
    const ESCAPE_CHAR = 0x7c;

    for (let i = 0; i < data.length; i++) {
      if (data[i] === ESCAPE_CHAR && i + 1 < data.length) {
        // 处理转义字符
        const nextByte = data[i + 1];
        switch (nextByte) {
          case 0x4e: // 0x7C 0x4E -> 0x7E
            result.push(0x7e);
            break;
          case 0x4d: // 0x7C 0x4D -> 0x7D
            result.push(0x7d);
            break;
          case 0x4c: // 0x7C 0x4C -> 0x7C
            result.push(0x7c);
            break;
          default:
            // 无效的转义序列，保持原样
            console.warn(`Invalid escape sequence: 0x${ESCAPE_CHAR.toString(16)} 0x${nextByte.toString(16)}`);
            result.push(data[i]);
            result.push(nextByte);
            break;
        }
        i++; // 跳过下一个字节
      } else {
        result.push(data[i]);
      }
    }

    return new Uint8Array(result);
  }
}

module.exports = MessageParser;
