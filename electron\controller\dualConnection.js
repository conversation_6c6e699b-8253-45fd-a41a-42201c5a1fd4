"use strict";

const { logger } = require("ee-core/log");
const DualConnectionService = require("../service/dualConnectionService");
const MessageRouter = require("../service/messageRouter");
const MessageEncoder = require("../utils/messageEncoder");

/**
 * 双连接管理控制器
 * 提供双连接状态查询、消息发送等功能的前端接口
 */
class DualConnectionController {
  /**
   * 获取连接状态
   */
  async getStatus(args, event) {
    try {
      const status = DualConnectionService.getConnectionStatus();
      logger.debug("[DualConnectionController] Connection status retrieved");
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to get connection status:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查服务健康状态
   */
  async getHealth(args, event) {
    try {
      const isHealthy = await DualConnectionService.isHealthy();
      return {
        success: true,
        data: { isHealthy }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to check health:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送消息
   * @param {Object} args - 参数对象
   * @param {number} args.messageId - 消息ID
   * @param {Object} args.body - 消息体
   * @param {string} args.target - 目标连接 ('auto', 'websocket', 'usb', 'all')
   */
  async sendMessage(args, event) {
    try {
      const { messageId, body, target = 'auto' } = args;

      if (!messageId || !body) {
        throw new Error("messageId and body are required");
      }

      let result;

      if (target === 'auto') {
        // 使用路由器决定发送目标
        const routeTarget = MessageRouter.getRouteTarget(messageId);
        
        if (messageId === 1100 || messageId === 2048) {
          // 双发消息
          result = await DualConnectionService.sendDualMessage(messageId, body);
        } else if (messageId === 20) {
          // 仅WebSocket
          result = await DualConnectionService.sendMessageID20(body);
        } else {
          // 构建消息并按路由规则发送
          const msgBody = await MessageEncoder.buildBody(messageId, body);
          const head = {
            vehicleType: 0,
            dataUnitLength: msgBody.length,
            transmitCount: MessageEncoder.getTransmitCount(messageId),
          };
          const msgHead = MessageEncoder.buildHead(head);
          const mergedBuffer = Buffer.concat([msgHead, msgBody]);
          const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);
          
          result = await DualConnectionService.sendMessage(usbBuffer, routeTarget);
        }
      } else {
        // 指定目标发送
        const msgBody = await MessageEncoder.buildBody(messageId, body);
        const head = {
          vehicleType: 0,
          dataUnitLength: msgBody.length,
          transmitCount: MessageEncoder.getTransmitCount(messageId),
        };
        const msgHead = MessageEncoder.buildHead(head);
        const mergedBuffer = Buffer.concat([msgHead, msgBody]);
        const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);
        
        result = await DualConnectionService.sendMessage(usbBuffer, target);
      }

      logger.info(`[DualConnectionController] Message ${messageId} sent successfully`);
      return {
        success: true,
        data: { 
          messageId, 
          target: target === 'auto' ? MessageRouter.getRouteTarget(messageId) : target,
          results: result 
        }
      };

    } catch (error) {
      logger.error("[DualConnectionController] Failed to send message:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送原始消息（已编码的Buffer）
   */
  async sendRawMessage(args, event) {
    try {
      const { message, target = 'all' } = args;

      if (!message) {
        throw new Error("message is required");
      }

      // 将消息转换为Buffer（如果不是的话）
      const messageBuffer = Buffer.isBuffer(message) ? message : Buffer.from(message);
      
      const result = await DualConnectionService.sendMessage(messageBuffer, target);

      return {
        success: true,
        data: { target, results: result }
      };

    } catch (error) {
      logger.error("[DualConnectionController] Failed to send raw message:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取消息路由规则
   */
  async getRoutingRules(args, event) {
    try {
      const rules = MessageRouter.getAllRules();
      const stats = MessageRouter.getStats();

      return {
        success: true,
        data: { rules, stats }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to get routing rules:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 添加路由规则
   */
  async addRoutingRule(args, event) {
    try {
      const { messageId, target } = args;

      if (!messageId || !target) {
        throw new Error("messageId and target are required");
      }

      if (!MessageRouter.isValidMessageId(messageId)) {
        throw new Error("Invalid messageId");
      }

      MessageRouter.addRule(messageId, target);

      return {
        success: true,
        data: { messageId, target }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to add routing rule:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 移除路由规则
   */
  async removeRoutingRule(args, event) {
    try {
      const { messageId } = args;

      if (!messageId) {
        throw new Error("messageId is required");
      }

      MessageRouter.removeRule(messageId);

      return {
        success: true,
        data: { messageId }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to remove routing rule:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 重置路由规则为默认值
   */
  async resetRoutingRules(args, event) {
    try {
      MessageRouter.resetToDefault();

      return {
        success: true,
        data: MessageRouter.getAllRules()
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to reset routing rules:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取指定消息ID的路由信息
   */
  async getMessageRouteInfo(args, event) {
    try {
      const { messageId } = args;

      if (!messageId) {
        throw new Error("messageId is required");
      }

      const routeInfo = MessageRouter.getMessageRouteInfo(messageId);

      return {
        success: true,
        data: routeInfo
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to get message route info:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 启动双连接服务
   */
  async start(args, event) {
    try {
      await DualConnectionService.start();
      
      return {
        success: true,
        data: { message: "Dual connection service started" }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to start service:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 停止双连接服务
   */
  async stop(args, event) {
    try {
      await DualConnectionService.stop();
      
      return {
        success: true,
        data: { message: "Dual connection service stopped" }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to stop service:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

DualConnectionController.toString = () => "[class DualConnectionController]";

module.exports = DualConnectionController;
