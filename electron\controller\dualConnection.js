"use strict";

const { logger } = require("ee-core/log");
const DualConnectionService = require("../service/dualConnectionService");
const MessageEncoder = require("../utils/messageEncoder");

/**
 * 双连接管理控制器
 * 提供双连接状态查询、消息发送等功能的前端接口
 */
class DualConnectionController {
  /**
   * 获取连接状态
   */
  async getStatus(args, event) {
    try {
      const status = DualConnectionService.getConnectionStatus();
      logger.debug("[DualConnectionController] Connection status retrieved");
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to get connection status:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查服务健康状态
   */
  async getHealth(args, event) {
    try {
      const isHealthy = await DualConnectionService.isHealthy();
      return {
        success: true,
        data: { isHealthy }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to check health:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送消息
   * @param {Object} args - 参数对象
   * @param {number} args.messageId - 消息ID
   * @param {Object} args.body - 消息体
   * @param {string} args.target - 目标连接 ('auto', 'websocket', 'usb', 'all')
   */
  async sendMessage(args, event) {
    try {
      const { messageId, body, target = 'auto' } = args;

      if (!messageId || !body) {
        throw new Error("messageId and body are required");
      }

      let result;
      let actualTarget;

      if (target === 'auto') {
        // 自动路由：1100和2048走双通道，其余只走网口
        result = await DualConnectionService.sendMessageWithId(messageId, body);
        actualTarget = DualConnectionService.getMessageTarget(messageId);
      } else {
        // 指定目标发送
        const msgBody = await MessageEncoder.buildBody(messageId, body);
        const head = {
          vehicleType: 0,
          dataUnitLength: msgBody.length,
          transmitCount: MessageEncoder.getTransmitCount(messageId),
        };
        const msgHead = MessageEncoder.buildHead(head);
        const mergedBuffer = Buffer.concat([msgHead, msgBody]);
        const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);

        result = await DualConnectionService.sendMessage(usbBuffer, target);
        actualTarget = target;
      }

      logger.info(`[DualConnectionController] Message ${messageId} sent successfully`);
      return {
        success: true,
        data: {
          messageId,
          target: actualTarget,
          results: result
        }
      };

    } catch (error) {
      logger.error("[DualConnectionController] Failed to send message:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送原始消息（已编码的Buffer）
   */
  async sendRawMessage(args, event) {
    try {
      const { message, target = 'all' } = args;

      if (!message) {
        throw new Error("message is required");
      }

      // 将消息转换为Buffer（如果不是的话）
      const messageBuffer = Buffer.isBuffer(message) ? message : Buffer.from(message);
      
      const result = await DualConnectionService.sendMessage(messageBuffer, target);

      return {
        success: true,
        data: { target, results: result }
      };

    } catch (error) {
      logger.error("[DualConnectionController] Failed to send raw message:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取指定消息ID的路由信息
   */
  async getMessageRouteInfo(args, event) {
    try {
      const { messageId } = args;

      if (!messageId) {
        throw new Error("messageId is required");
      }

      const target = DualConnectionService.getMessageTarget(messageId);
      const isDual = (messageId === 1100 || messageId === 2048);

      const routeInfo = {
        messageId,
        target,
        sendToWebSocket: true, // 所有消息都发送到WebSocket
        sendToUSB: isDual,     // 只有1100和2048发送到USB
        isDual
      };

      return {
        success: true,
        data: routeInfo
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to get message route info:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 启动双连接服务
   */
  async start(args, event) {
    try {
      await DualConnectionService.start();
      
      return {
        success: true,
        data: { message: "Dual connection service started" }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to start service:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 停止双连接服务
   */
  async stop(args, event) {
    try {
      await DualConnectionService.stop();
      
      return {
        success: true,
        data: { message: "Dual connection service stopped" }
      };
    } catch (error) {
      logger.error("[DualConnectionController] Failed to stop service:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

DualConnectionController.toString = () => "[class DualConnectionController]";

module.exports = DualConnectionController;
