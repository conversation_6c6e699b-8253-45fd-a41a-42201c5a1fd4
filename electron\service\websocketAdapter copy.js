"use strict";

const { logger } = require("ee-core/log");
const EventEmitter = require("events");
const WebSocket = require("ws");
const MessageParser = require("../utils/messageParser");
const MessageEncoder = require("../utils/messageEncoder");
const globalStateManager = require("./globalStateManager");
const { SenderStatus } = require("../utils/constants");

/**
 * WebSocket连接适配器
 * 将现有的WebSocket服务包装成符合连接管理器接口的适配器
 */
class WebSocketAdapter extends EventEmitter {
  constructor() {
    super();
    
    this.url = "";
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;
    
    // 重连配置
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 10000;
    this.reconnectTimer = null;
    
    // 消息发送配置
    this.messageTimer1100 = null;
    this.messageInterval1100 = 20000;
    
    // 健康检查配置
    this.lastMessageTime = Date.now();
    this.healthCheckTimeout = 30000; // 30秒无消息则认为不健康
  }

  /**
   * 连接WebSocket
   */
  async connect() {
    if (this.isConnected || this.isConnecting) {
      logger.warn("[WebSocketAdapter] Already connected or connecting");
      return;
    }

    this.isConnecting = true;
    this.emit('connecting');

    try {
      // 获取连接配置
      const androidUrl = await globalStateManager.get("pad.androidUrl");
      const androidWSPort = await globalStateManager.get("pad.androidWSPort");
      this.url = `ws://${androidUrl}:${androidWSPort}`;

      // 创建WebSocket连接
      await this._createWebSocketConnection();
      
      logger.info(`[WebSocketAdapter] Connected to WebSocket server: ${this.url}`);
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      
      // 启动消息发送
      this._startSendMessage();
      
      this.emit('connected');
      
    } catch (error) {
      this.isConnecting = false;
      logger.error("[WebSocketAdapter] Failed to connect:", error);
      this.emit('error', error);
      this._scheduleReconnect();
      throw error;
    }
  }

  /**
   * 断开WebSocket连接
   */
  async disconnect() {
    logger.info("[WebSocketAdapter] Disconnecting WebSocket");
    
    this._stopSendMessage();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    
    this.emit('disconnected');
    logger.info("[WebSocketAdapter] WebSocket disconnected");
  }

  /**
   * 发送消息
   * @param {Buffer} message - 要发送的消息
   */
  async sendMessage(message) {
    if (!this.isConnected || !this.socket || this.socket.readyState !== WebSocket.OPEN) {
      throw new Error("WebSocket connection not available");
    }

    return new Promise((resolve, reject) => {
      try {
        this.socket.send(message);
        logger.debug(`[WebSocketAdapter] Message sent: ${message.length} bytes`);
        resolve();
      } catch (error) {
        logger.error("[WebSocketAdapter] Failed to send message:", error);
        reject(error);
      }
    });
  }

  /**
   * 检查连接健康状态
   */
  async isHealthy() {
    if (!this.isConnected || !this.socket || this.socket.readyState !== WebSocket.OPEN) {
      return false;
    }
    
    // 检查最后消息时间
    const timeSinceLastMessage = Date.now() - this.lastMessageTime;
    if (timeSinceLastMessage > this.healthCheckTimeout) {
      logger.warn(`[WebSocketAdapter] No messages received for ${timeSinceLastMessage}ms`);
      return false;
    }
    
    return true;
  }

  /**
   * 获取连接信息
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      url: this.url,
      reconnectAttempts: this.reconnectAttempts,
      lastMessageTime: this.lastMessageTime,
      readyState: this.socket ? this.socket.readyState : null
    };
  }

  /**
   * 创建WebSocket连接
   */
  async _createWebSocketConnection() {
    return new Promise((resolve, reject) => {
      this.socket = new WebSocket(this.url);

      // 连接成功事件
      this.socket.onopen = () => {
        logger.info("[WebSocketAdapter] WebSocket connection opened");
        resolve();
      };

      // 接收消息事件
      this.socket.onmessage = (event) => {
        console.log(event.data);
        this._handleMessage(event.data);
      };

      // 连接关闭事件
      this.socket.onclose = () => {
        logger.info("[WebSocketAdapter] WebSocket connection closed");
        this.isConnected = false;
        this.emit('disconnected');
        this._scheduleReconnect();
      };

      // 错误事件
      this.socket.onerror = (error) => {
        logger.error("[WebSocketAdapter] WebSocket error:", error);
        this.emit('error', error);
        if (!this.isConnected) {
          reject(error);
        }
      };
    });
  }

  /**
   * 处理接收到的消息
   * @param {*} data - 接收到的数据
   */
  async _handleMessage(data) {
    try {
      this.lastMessageTime = Date.now();
      
      // 解析消息
      const message = MessageParser.parse(data, "usb");
      
      
      // 发送消息事件
      this.emit('message', message);
      console.log(message);
      
      
      // 处理特定类型的消息
      switch (message.id) {
        case 2048:
          logger.debug("[WebSocketAdapter] Heartbeat message received");
          break;
        case 20:
          await this._handleMessageID20(message);
          break;
        // case 11: // 2025年9月3日 取消心跳
        //   await this._handleMessageID11(message);
        //   break;
        default:
          logger.debug(`[WebSocketAdapter] Received message ID: ${message.id}`);
          break;
      }
    } catch (error) {
      logger.error("[WebSocketAdapter] Failed to parse message:", error);
    }
  }

  /**
   * 处理ID为20的消息
   * @param {object} message - 消息对象
   */
  async _handleMessageID20(message) {
    logger.debug("[WebSocketAdapter] Handling message ID 20");
    
    // 如果是页面300的消息，更新全局状态
    if (message.jsonData && message.jsonData.page === 300 && message.jsonData.payload) {
      try {
        await globalStateManager.setAll(message.jsonData.payload);
        logger.info("[WebSocketAdapter] Global state updated from WebSocket message");
      } catch (error) {
        logger.error("[WebSocketAdapter] Error updating global state:", error);
      }
    }
  }

  /**
   * 处理ID为11的消息
   * @param {object} message - 消息对象
   */
  async _handleMessageID11(message) {
    logger.debug("[WebSocketAdapter] Handling message ID 11");
    
    // 更新pad状态
    if (message["senderStatus"]) {
      let status = 8;
      switch (message["senderStatus"]) {
        case SenderStatus.INITIALIZE:
          status = SenderStatus.INITIALIZE;
          break;
        case SenderStatus.WAITING:
        case SenderStatus.DEBUGGING:
        case SenderStatus.ASYNC_MONITORING:
          status = SenderStatus.WAITING;
          break;
        case SenderStatus.LOGIN:
        case SenderStatus.REMOTE_CONTROL:
        case SenderStatus.LOCKED:
        case SenderStatus.FAILSAFE:
        case SenderStatus.LOOPBACK_TEST:
          status = SenderStatus.REMOTE_CONTROL;
          break;
      }
      
      const currentStatus = await globalStateManager.get("pad.status");
      if (currentStatus !== status) {
        await globalStateManager.set("pad.status", status);
      }
    }
    
    // 发送心跳响应
    await this._sendHeartbeatResponse(message);
  }

  /**
   * 发送心跳响应
   * @param {object} message - 接收到的消息
   */
  async _sendHeartbeatResponse(message) {
    try {
      const padStatus = await globalStateManager.get("pad.status");
      
      const body = {
        senderStatus: padStatus,
        confirmedTransmitCount: message.transmitCount,
      };
      
      const msgBody = await MessageEncoder.buildBody(12, body);
      const head = {
        vehicleType: 0,
        dataUnitLength: msgBody.length,
        transmitCount: MessageEncoder.getTransmitCount(11),
      };
      
      const msgHead = MessageEncoder.buildHead(head);
      const mergedBuffer = Buffer.concat([msgHead, msgBody]);
      
      await this.sendMessage(mergedBuffer);
      logger.debug("[WebSocketAdapter] Heartbeat response sent");
      
    } catch (error) {
      logger.error("[WebSocketAdapter] Failed to send heartbeat response:", error);
    }
  }

  /**
   * 启动定时消息发送
   */
  _startSendMessage() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
    }

    this.messageTimer1100 = setInterval(async () => {
      try {
        const body = {
          vehicleSubType: 0,
          tabletStatus: 64, // 64远控
          failureLevel: 255, // 255没有失效
        };

        const msgBody = await MessageEncoder.buildBody(1100, body);
        const head = {
          vehicleType: 0,
          dataUnitLength: msgBody.length,
          transmitCount: MessageEncoder.getTransmitCount(1100),
        };

        const msgHead = MessageEncoder.buildHead(head);
        const mergedBuffer = Buffer.concat([msgHead, msgBody]);

        await this.sendMessage(mergedBuffer);
        logger.debug("[WebSocketAdapter] Periodic message sent");

      } catch (error) {
        logger.error("[WebSocketAdapter] Failed to send periodic message:", error);
      }
    }, this.messageInterval1100);

    // 延迟获取所有数据
    setTimeout(() => {
      this._getAllDataFromAndroid();
    }, 5000);
  }

  /**
   * 停止定时消息发送
   */
  _stopSendMessage() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
      this.messageTimer1100 = null;
    }
  }

  /**
   * 获取所有Android数据
   */
  async _getAllDataFromAndroid() {
    try {
      const body = {
        communicationType: 2,
        jsonData: {
          page: 300,
          type: "params",
          payload: {
            route: "all",
          },
        },
      };

      await this._sendMessageID20(body);
    } catch (error) {
      logger.error("[WebSocketAdapter] Failed to get all data from Android:", error);
    }
  }

  /**
   * 发送ID为20的消息
   * @param {object} body - 消息体
   */
  async _sendMessageID20(body) {
    console.log(body);
    
    const msgBody = await MessageEncoder.buildBody(20, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(20),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);
    const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);

    await this.sendMessage(usbBuffer);
    logger.debug("[WebSocketAdapter] Message ID 20 sent");
  }

  /**
   * 安排重连
   */
  _scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.warn("[WebSocketAdapter] Max reconnect attempts reached");
      return;
    }

    if (this.reconnectTimer) {
      return; // 已经在重连中
    }

    this.reconnectAttempts++;

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null;
      logger.info(`[WebSocketAdapter] Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      try {
        await this.connect();
      } catch (error) {
        logger.error("[WebSocketAdapter] Reconnect failed:", error);
        this._scheduleReconnect();
      }
    }, this.reconnectInterval);
  }
}

module.exports = WebSocketAdapter;
