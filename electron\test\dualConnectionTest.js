"use strict";

/**
 * 双连接服务测试文件
 * 用于验证DualConnectionService和MessageRouter的功能
 */

const DualConnectionService = require("../service/dualConnectionService");
const MessageRouter = require("../service/messageRouter");
const { logger } = require("ee-core/log");

class DualConnectionTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    logger.info("[DualConnectionTest] Starting all tests");

    try {
      await this.testMessageRouter();
      await this.testDualConnectionService();
      
      this.printResults();
    } catch (error) {
      logger.error("[DualConnectionTest] Test execution failed:", error);
    }
  }

  /**
   * 测试消息路由器
   */
  async testMessageRouter() {
    logger.info("[DualConnectionTest] Testing MessageRouter");

    // 测试默认路由规则
    this.assert(
      MessageRouter.getRouteTarget(1100) === 'all',
      "ID 1100 should route to 'all'"
    );

    this.assert(
      MessageRouter.getRouteTarget(2048) === 'all',
      "ID 2048 should route to 'all'"
    );

    this.assert(
      MessageRouter.getRouteTarget(20) === 'websocket',
      "ID 20 should route to 'websocket'"
    );

    // 测试添加自定义规则
    MessageRouter.addRule(999, 'usb');
    this.assert(
      MessageRouter.getRouteTarget(999) === 'usb',
      "Custom rule: ID 999 should route to 'usb'"
    );

    // 测试shouldSendTo方法
    this.assert(
      MessageRouter.shouldSendTo(1100, 'websocket') === true,
      "ID 1100 should send to websocket"
    );

    this.assert(
      MessageRouter.shouldSendTo(1100, 'usb') === true,
      "ID 1100 should send to usb"
    );

    this.assert(
      MessageRouter.shouldSendTo(20, 'usb') === false,
      "ID 20 should not send to usb"
    );

    // 测试统计信息
    const stats = MessageRouter.getStats();
    this.assert(
      stats.totalRules > 0,
      "Should have routing rules"
    );

    // 测试重置
    MessageRouter.resetToDefault();
    this.assert(
      MessageRouter.getRouteTarget(999) === 'websocket',
      "After reset, unknown ID should default to websocket"
    );

    logger.info("[DualConnectionTest] MessageRouter tests completed");
  }

  /**
   * 测试双连接服务
   */
  async testDualConnectionService() {
    logger.info("[DualConnectionTest] Testing DualConnectionService");

    // 测试初始状态
    const initialStatus = DualConnectionService.getConnectionStatus();
    this.assert(
      typeof initialStatus === 'object',
      "Should return connection status object"
    );

    this.assert(
      initialStatus.hasOwnProperty('isStarted'),
      "Status should have isStarted property"
    );

    this.assert(
      initialStatus.hasOwnProperty('websocket'),
      "Status should have websocket property"
    );

    this.assert(
      initialStatus.hasOwnProperty('usb'),
      "Status should have usb property"
    );

    // 测试健康检查（服务未启动时应该返回false）
    const healthBeforeStart = await DualConnectionService.isHealthy();
    this.assert(
      healthBeforeStart === false,
      "Should be unhealthy when not started"
    );

    // 注意：实际的连接测试需要真实的WebSocket服务器和USB设备
    // 这里只测试API接口的可用性

    logger.info("[DualConnectionTest] DualConnectionService tests completed");
  }

  /**
   * 测试消息路由信息
   */
  testMessageRouteInfo() {
    logger.info("[DualConnectionTest] Testing message route info");

    const routeInfo1100 = MessageRouter.getMessageRouteInfo(1100);
    this.assert(
      routeInfo1100.messageId === 1100,
      "Route info should contain correct messageId"
    );

    this.assert(
      routeInfo1100.isDual === true,
      "ID 1100 should be dual route"
    );

    this.assert(
      routeInfo1100.sendToWebSocket === true,
      "ID 1100 should send to WebSocket"
    );

    this.assert(
      routeInfo1100.sendToUSB === true,
      "ID 1100 should send to USB"
    );

    const routeInfo20 = MessageRouter.getMessageRouteInfo(20);
    this.assert(
      routeInfo20.isDual === false,
      "ID 20 should not be dual route"
    );

    this.assert(
      routeInfo20.sendToWebSocket === true,
      "ID 20 should send to WebSocket"
    );

    this.assert(
      routeInfo20.sendToUSB === false,
      "ID 20 should not send to USB"
    );
  }

  /**
   * 测试批量规则操作
   */
  testBatchRules() {
    logger.info("[DualConnectionTest] Testing batch rules");

    const batchRules = [
      { messageId: 100, target: 'websocket' },
      { messageId: 200, target: 'usb' },
      { messageId: 300, target: 'all' }
    ];

    MessageRouter.addBatchRules(batchRules);

    this.assert(
      MessageRouter.getRouteTarget(100) === 'websocket',
      "Batch rule 100 should route to websocket"
    );

    this.assert(
      MessageRouter.getRouteTarget(200) === 'usb',
      "Batch rule 200 should route to usb"
    );

    this.assert(
      MessageRouter.getRouteTarget(300) === 'all',
      "Batch rule 300 should route to all"
    );

    // 清理测试规则
    MessageRouter.removeRule(100);
    MessageRouter.removeRule(200);
    MessageRouter.removeRule(300);
  }

  /**
   * 断言方法
   */
  assert(condition, message) {
    const result = {
      condition,
      message,
      passed: !!condition,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);

    if (result.passed) {
      logger.info(`[TEST PASS] ${message}`);
    } else {
      logger.error(`[TEST FAIL] ${message}`);
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    logger.info(`[DualConnectionTest] Test Results:`);
    logger.info(`  Total: ${totalTests}`);
    logger.info(`  Passed: ${passedTests}`);
    logger.info(`  Failed: ${failedTests}`);
    logger.info(`  Success Rate: ${((passedTests / totalTests) * 100).toFixed(2)}%`);

    if (failedTests > 0) {
      logger.error(`[DualConnectionTest] Failed tests:`);
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => logger.error(`  - ${r.message}`));
    }
  }

  /**
   * 获取测试结果
   */
  getResults() {
    return {
      total: this.testResults.length,
      passed: this.testResults.filter(r => r.passed).length,
      failed: this.testResults.filter(r => !r.passed).length,
      results: this.testResults
    };
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new DualConnectionTest();
  test.runAllTests().then(() => {
    process.exit(0);
  }).catch((error) => {
    logger.error("Test execution failed:", error);
    process.exit(1);
  });
}

module.exports = DualConnectionTest;
