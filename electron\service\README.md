# 双连接通信服务

## 概述

本项目将原有的四个连接管理文件简化为两个核心文件，实现WebSocket和USB同时连接，使用统一的USB协议进行通信。

## 文件结构

### 新增文件
- `dualConnectionService.js` - 主要的双连接通信服务
- `messageRouter.js` - 消息路由管理器
- `dualConnection.js` (controller) - 前端接口控制器
- `migrationGuide.md` - 迁移指南
- `dualConnectionTest.js` - 测试文件

### 原有文件（可删除）
- `connectionManager.js`
- `unifiedCommunicationService.js` 
- `usbService.js`
- `websocketAdapter.js`

## 核心特性

### 1. 双连接架构
- WebSocket和USB同时保持连接
- 不再有主备切换，两个连接独立工作
- 统一使用USB协议格式（packUsbMessage和escapeUsbData）

### 2. 智能消息路由
- **ID 1100, 2048**: 同时发送到WebSocket和USB
- **ID 20, 11, 12**: 仅发送到WebSocket  
- **其他消息**: 默认仅发送到WebSocket
- 支持动态配置路由规则

### 3. 自动重连机制
- WebSocket和USB连接独立重连
- 可配置重连次数和间隔
- 连接失败不影响另一个连接

## 使用方法

### 基本使用

```javascript
const DualConnectionService = require("./service/dualConnectionService");

// 启动服务
await DualConnectionService.start();

// 发送双发消息（ID 1100, 2048）
await DualConnectionService.sendDualMessage(1100, {
  vehicleSubType: 0,
  tabletStatus: 64,
  failureLevel: 255
});

// 发送WebSocket专用消息（ID 20）
await DualConnectionService.sendMessageID20({
  communicationType: 2,
  jsonData: { page: 300, type: "params" }
});

// 发送到指定连接
await DualConnectionService.sendMessage(buffer, 'websocket'); // 仅WebSocket
await DualConnectionService.sendMessage(buffer, 'usb');       // 仅USB
await DualConnectionService.sendMessage(buffer, 'all');       // 两个连接

// 获取连接状态
const status = DualConnectionService.getConnectionStatus();

// 检查健康状态
const isHealthy = await DualConnectionService.isHealthy();

// 停止服务
await DualConnectionService.stop();
```

### 消息路由管理

```javascript
const MessageRouter = require("./service/messageRouter");

// 获取消息路由目标
const target = MessageRouter.getRouteTarget(1100); // 返回 'all'

// 检查是否应该发送到指定连接
const shouldSend = MessageRouter.shouldSendTo(20, 'usb'); // 返回 false

// 添加自定义路由规则
MessageRouter.addRule(999, 'usb'); // ID 999 仅发送到USB

// 批量添加规则
MessageRouter.addBatchRules([
  { messageId: 100, target: 'websocket' },
  { messageId: 200, target: 'all' }
]);

// 获取所有规则
const rules = MessageRouter.getAllRules();

// 重置为默认规则
MessageRouter.resetToDefault();
```

### 前端控制器使用

```javascript
// 通过IPC调用
const result = await window.ipc.invoke('dual-connection:sendMessage', {
  messageId: 1100,
  body: { vehicleSubType: 0, tabletStatus: 64 },
  target: 'auto' // 'auto', 'websocket', 'usb', 'all'
});

// 获取连接状态
const status = await window.ipc.invoke('dual-connection:getStatus');

// 管理路由规则
await window.ipc.invoke('dual-connection:addRoutingRule', {
  messageId: 999,
  target: 'usb'
});
```

## 配置选项

### 连接配置
```javascript
{
  maxReconnectAttempts: 5,      // 最大重连次数
  reconnectInterval: 3000,      // 重连间隔（毫秒）
  healthCheckTimeout: 30000,    // 健康检查超时（毫秒）
  messageInterval1100: 20000,   // ID1100消息发送间隔（毫秒）
  usbBaudRate: 115200          // USB波特率
}
```

### 路由规则配置
```javascript
{
  dual: [1100, 2048],           // 双发消息ID
  websocketOnly: [20, 11, 12],  // 仅WebSocket消息ID
  usbOnly: []                   // 仅USB消息ID
}
```

## 事件监听

### 前端事件
```javascript
// 双连接状态变化
window.ipc.on("dual-connection-status", (data) => {
  console.log("服务状态:", data.status);
  console.log("连接详情:", data.connectionStatus);
});

// 消息接收（保持原有格式）
window.ipc.on("ipc-message", (data) => {
  console.log(`从 ${data.connectionType} 接收消息:`, data);
});
```

## 迁移步骤

1. **更新lifecycle.js**
   ```javascript
   // 替换引用
   const DualConnectionService = require("../service/dualConnectionService");
   
   // 更新启动/停止调用
   await DualConnectionService.start();
   await DualConnectionService.stop();
   ```

2. **更新控制器**
   - 使用新的`dualConnection.js`控制器
   - 更新IPC方法名称

3. **更新前端代码**
   - 修改事件监听器名称
   - 适配新的状态结构

4. **测试验证**
   - 运行测试文件验证功能
   - 测试各种连接场景

## 测试

```bash
# 运行测试
node electron/test/dualConnectionTest.js
```

测试覆盖：
- 消息路由规则
- 连接状态管理
- API接口可用性
- 批量操作功能

## 注意事项

1. **协议统一**: 所有消息都使用USB协议格式
2. **资源使用**: 双连接会增加网络和系统资源消耗
3. **错误隔离**: 单个连接失败不影响另一个连接
4. **消息去重**: 接收端需要处理可能的重复消息
5. **配置兼容**: 保持与现有配置系统的兼容性

## 性能优化建议

1. 合理配置重连参数避免频繁重连
2. 根据实际需求调整消息发送间隔
3. 监控连接状态避免资源浪费
4. 定期清理无用的路由规则

## 故障排除

1. **连接失败**: 检查网络配置和USB设备状态
2. **消息丢失**: 验证路由规则和连接状态
3. **性能问题**: 调整配置参数和监控资源使用
4. **协议错误**: 确认消息格式和编码正确性
