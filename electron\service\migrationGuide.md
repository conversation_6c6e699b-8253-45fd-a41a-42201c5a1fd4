# 双连接服务迁移指南

## 概述

原有的四个文件已简化为两个主要文件：
- `dualConnectionService.js` - 主要的双连接通信服务
- `messageRouter.js` - 消息路由管理器

## 主要变更

### 1. 架构简化
- **原有**: ConnectionManager + UnifiedCommunicationService + WebSocketAdapter + UsbService
- **新架构**: DualConnectionService + MessageRouter

### 2. 连接策略变更
- **原有**: WebSocket优先，USB作为备用连接，自动切换
- **新架构**: WebSocket和USB同时连接，根据消息ID决定发送目标

### 3. 消息路由规则
- **ID 1100, 2048**: 同时发送到WebSocket和USB
- **ID 20, 11, 12**: 仅发送到WebSocket
- **其他消息**: 默认仅发送到WebSocket

## 迁移步骤

### 1. 更新lifecycle.js

```javascript
// 原有代码
const UnifiedCommunicationService = require("../service/unifiedCommunicationService");

// 替换为
const DualConnectionService = require("../service/dualConnectionService");

// 在ready()方法中
// 原有
await UnifiedCommunicationService.start();

// 替换为
await DualConnectionService.start();

// 在beforeClose()方法中
// 原有
await UnifiedCommunicationService.stop();

// 替换为
await DualConnectionService.stop();
```

### 2. 更新controller/connection.js

```javascript
// 原有代码
const UnifiedCommunicationService = require("../service/unifiedCommunicationService");

// 替换为
const DualConnectionService = require("../service/dualConnectionService");

// 更新方法调用
class ConnectionController {
  async getStatus(args, event) {
    try {
      // 原有
      const status = UnifiedCommunicationService.getConnectionStatus();
      
      // 替换为
      const status = DualConnectionService.getConnectionStatus();
      
      return { success: true, data: status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async sendMessage(args, event) {
    try {
      const { messageId, body, target = 'auto' } = args;
      
      if (target === 'auto') {
        // 使用路由器决定发送目标
        const MessageRouter = require("../service/messageRouter");
        const routeTarget = MessageRouter.getRouteTarget(messageId);
        
        if (messageId === 1100 || messageId === 2048) {
          await DualConnectionService.sendDualMessage(messageId, body);
        } else if (messageId === 20) {
          await DualConnectionService.sendMessageID20(body);
        } else {
          // 构建消息并发送
          const MessageEncoder = require("../utils/messageEncoder");
          const msgBody = await MessageEncoder.buildBody(messageId, body);
          const head = {
            vehicleType: 0,
            dataUnitLength: msgBody.length,
            transmitCount: MessageEncoder.getTransmitCount(messageId),
          };
          const msgHead = MessageEncoder.buildHead(head);
          const mergedBuffer = Buffer.concat([msgHead, msgBody]);
          const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);
          
          await DualConnectionService.sendMessage(usbBuffer, routeTarget);
        }
      } else {
        // 指定目标发送
        await DualConnectionService.sendMessage(args.message, target);
      }
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
```

### 3. 前端事件监听更新

```javascript
// 原有事件
window.ipc.on("unified-communication-status", (data) => {
  // 处理统一通信状态
});

window.ipc.on("connection-state-changed", (data) => {
  // 处理连接状态变化
});

// 新增事件
window.ipc.on("dual-connection-status", (data) => {
  // 处理双连接状态
  console.log("双连接状态:", data.status);
  console.log("连接详情:", data.connectionStatus);
});

// 消息接收保持不变
window.ipc.on("ipc-message", (data) => {
  // data.connectionType 现在可能是 'websocket' 或 'usb'
  console.log(`从 ${data.connectionType} 接收到消息:`, data);
});
```

## API对比

### 原有API
```javascript
// UnifiedCommunicationService
await service.start();
await service.stop();
await service.sendMessage(message);
await service.sendMessageID20(body);
service.getConnectionStatus();
await service.forceSwitch(type);
await service.isHealthy();
```

### 新API
```javascript
// DualConnectionService
await service.start();
await service.stop();
await service.sendMessage(message, target); // target: 'websocket', 'usb', 'all'
await service.sendDualMessage(messageId, body); // 1100, 2048
await service.sendMessageID20(body); // 仅WebSocket
service.getConnectionStatus();
await service.isHealthy();

// MessageRouter
const router = require("../service/messageRouter");
router.getRouteTarget(messageId);
router.shouldSendTo(messageId, connectionType);
router.addRule(messageId, target);
router.getAllRules();
```

## 配置变更

### 连接状态结构
```javascript
// 原有状态
{
  isStarted: true,
  activeConnection: "websocket",
  connections: {
    websocket: { state: "connected", isActive: true },
    usb: { state: "disconnected", isActive: false }
  }
}

// 新状态
{
  isStarted: true,
  websocket: {
    isConnected: true,
    isConnecting: false,
    url: "ws://*************:8080",
    reconnectAttempts: 0,
    lastMessageTime: **********
  },
  usb: {
    isConnected: true,
    isConnecting: false,
    portPath: "/dev/ttyUSB0",
    reconnectAttempts: 0,
    lastMessageTime: **********
  }
}
```

## 注意事项

1. **消息协议统一**: 所有消息都使用USB协议格式（packUsbMessage和escapeUsbData）
2. **同时连接**: WebSocket和USB会同时保持连接，不再有主备切换
3. **路由规则**: 可以通过MessageRouter动态调整消息路由规则
4. **错误处理**: 单个连接失败不会影响另一个连接的正常工作
5. **性能考虑**: 双连接会增加网络和系统资源使用

## 测试建议

1. 测试WebSocket单独连接
2. 测试USB单独连接  
3. 测试双连接同时工作
4. 测试消息路由规则
5. 测试连接断开重连机制
6. 测试不同消息ID的发送目标
