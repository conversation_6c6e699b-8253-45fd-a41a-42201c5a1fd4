"use strict";

const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const { ConnectionManager, ConnectionType } = require("./connectionManager");
const WebSocketAdapter = require("./websocketAdapter");
const UsbService = require("./usbService");
const MessageEncoder = require("../utils/messageEncoder");

/**
 * 统一通信服务
 * 管理WebSocket和USB连接的自动切换，提供统一的通信接口
 */
class UnifiedCommunicationService {
  static instance = null;

  static getInstance() {
    if (!UnifiedCommunicationService.instance) {
      UnifiedCommunicationService.instance = new UnifiedCommunicationService();
    }
    return UnifiedCommunicationService.instance;
  }

  constructor() {
    if (UnifiedCommunicationService.instance) {
      return UnifiedCommunicationService.instance;
    }

    this.connectionManager = ConnectionManager.getInstance();
    this.websocketAdapter = new WebSocketAdapter();
    this.usbService = UsbService;

    this.isStarted = false;

    UnifiedCommunicationService.instance = this;

    this._initializeConnections();
    this._bindEvents();
  }

  /**
   * 初始化连接
   */
  _initializeConnections() {
    // 注册WebSocket连接
    this.connectionManager.registerConnection(ConnectionType.WEBSOCKET, this.websocketAdapter);

    // 注册USB连接
    // this.connectionManager.registerConnection(ConnectionType.USB, this.usbService);
  }

  /**
   * 绑定事件
   */
  _bindEvents() {
    // 监听连接管理器事件
    this.connectionManager.on("connectionStateChanged", this._handleConnectionStateChange.bind(this));
    this.connectionManager.on("connectionError", this._handleConnectionError.bind(this));
    this.connectionManager.on("messageReceived", this._handleMessageReceived.bind(this));
  }

  /**
   * 启动统一通信服务
   */
  async start() {
    if (this.isStarted) {
      logger.warn("[UnifiedCommunicationService] Service already started");
      return;
    }

    try {
      logger.info("[UnifiedCommunicationService] Starting unified communication service");

      // 启动连接管理器
      await this.connectionManager.start();

      this.isStarted = true;
      logger.info("[UnifiedCommunicationService] Unified communication service started");

      // 通知前端服务已启动
      this._notifyServiceStatus("started");
    } catch (error) {
      logger.error("[UnifiedCommunicationService] Failed to start service:", error);
      throw error;
    }
  }

  /**
   * 停止统一通信服务
   */
  async stop() {
    if (!this.isStarted) {
      logger.warn("[UnifiedCommunicationService] Service not started");
      return;
    }

    try {
      logger.info("[UnifiedCommunicationService] Stopping unified communication service");

      // 停止连接管理器
      await this.connectionManager.stop();

      this.isStarted = false;
      logger.info("[UnifiedCommunicationService] Unified communication service stopped");

      // 通知前端服务已停止
      this._notifyServiceStatus("stopped");
    } catch (error) {
      logger.error("[UnifiedCommunicationService] Failed to stop service:", error);
      throw error;
    }
  }

  /**
   * 发送消息
   * @param {*} message - 要发送的消息
   */
  async sendMessage(message) {
    if (!this.isStarted) {
      throw new Error("Unified communication service not started");
    }

    return await this.connectionManager.sendMessage(message);
  }

  /**
   * 发送ID为20的消息
   * @param {object} body - 消息体
   */
  async sendMessageID20(body) {
    const msgBody = await MessageEncoder.buildBody(20, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(20),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);
    const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);

    console.log("usbBuffer", usbBuffer);

    return await this.sendMessage(usbBuffer);
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isStarted: this.isStarted,
      ...this.connectionManager.getConnectionStatus(),
    };
  }

  /**
   * 强制切换连接类型
   * @param {string} type - 连接类型 ('websocket' 或 'usb')
   */
  async forceSwitch(type) {
    if (!this.isStarted) {
      throw new Error("Unified communication service not started");
    }

    const connectionType = type === "websocket" ? ConnectionType.WEBSOCKET : ConnectionType.USB;
    return await this.connectionManager.forceSwitch(connectionType);
  }

  /**
   * 检查服务健康状态
   */
  async isHealthy() {
    if (!this.isStarted) {
      return false;
    }

    const status = this.connectionManager.getConnectionStatus();
    return status.activeConnection !== null;
  }

  /**
   * 处理连接状态变化
   * @param {object} event - 状态变化事件
   */
  _handleConnectionStateChange(event) {
    const { type, oldState, newState } = event;

    logger.info(`[UnifiedCommunicationService] Connection ${type} state changed: ${oldState} -> ${newState}`);

    // 通知前端连接状态变化
    this._notifyConnectionStateChange(event);
  }

  /**
   * 处理连接错误
   * @param {object} event - 错误事件
   */
  _handleConnectionError(event) {
    const { type, error } = event;

    logger.error(`[UnifiedCommunicationService] Connection ${type} error:`, error);

    // 通知前端连接错误
    this._notifyConnectionError(event);
  }

  /**
   * 处理接收到的消息
   * @param {object} event - 消息事件
   */
  _handleMessageReceived(event) {
    const { type, message } = event;

    logger.debug(`[UnifiedCommunicationService] Message received from ${type}:`, message.id);

    // 转发消息到前端（保持与原有WebSocket服务的兼容性）
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("ipc-message", {
        ...message,
        connectionType: type,
      });
    }
  }

  /**
   * 通知前端服务状态
   * @param {string} status - 服务状态
   */
  _notifyServiceStatus(status) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("unified-communication-status", {
        status,
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 通知前端连接状态变化
   * @param {object} event - 状态变化事件
   */
  _notifyConnectionStateChange(event) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("connection-state-changed", {
        ...event,
        timestamp: Date.now(),
        connectionStatus: this.getConnectionStatus(),
      });
    }
  }

  /**
   * 通知前端连接错误
   * @param {object} event - 错误事件
   */
  _notifyConnectionError(event) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("connection-error", {
        ...event,
        timestamp: Date.now(),
      });

      // 显示错误通知
      mainWindow.webContents.send("show-notification", {
        type: "warning",
        content: `连接 ${event.type} 出现错误，正在尝试切换到备用连接`,
      });
    }
  }

  /**
   * 获取详细的连接信息
   */
  getDetailedConnectionInfo() {
    return {
      service: {
        isStarted: this.isStarted,
        startTime: this.startTime,
      },
      websocket: this.websocketAdapter.getConnectionInfo(),
      usb: this.usbService.getConnectionInfo(),
      manager: this.connectionManager.getConnectionStatus(),
    };
  }
}

module.exports = UnifiedCommunicationService.getInstance();
