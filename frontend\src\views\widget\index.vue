<template>
  <div class="widget-layout-editor">
    <!-- 标题栏 -->
    <div class="header">
      <h2 class="title">组件布局调整</h2>
      <div class="actions">
        <v-btn color="error" @click="handleCancel">取消</v-btn>
        <v-btn color="primary" @click="handleApply" class="ml-2">应用</v-btn>
      </div>
    </div>

    <div class="content">
      <!-- 画布区域 -->
      <div class="canvas-container">
        <div class="canvas-wrapper">
          <canvas id="widgetCanvas" ref="canvasRef"></canvas>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="control-panel">
        <v-card class="pa-4">
          <v-card-title>控制面板</v-card-title>

          <!-- 组件列表 -->
          <div class="widget-list mb-4">
            <h4 class="mb-2">组件列表</h4>
            <div v-for="(widget, key) in widgets" :key="key" class="widget-item mb-2">
              <div class="d-flex align-center">
                <v-checkbox
                  v-model="widget.isShow"
                  :label="getWidgetLabel(key)"
                  @change="updateWidgetVisibility(key)"
                  density="compact"
                ></v-checkbox>
              </div>
            </div>
          </div>

          <!-- 当前选中组件的属性 -->
          <div v-if="selectedWidget" class="widget-properties">
            <h4 class="mb-2">{{ getWidgetLabel(selectedWidget) }} 属性</h4>

            <v-row dense>
              <v-col cols="6">
                <v-text-field
                  v-model.number="widgets[selectedWidget].left"
                  label="左边距"
                  type="number"
                  suffix="px"
                  @input="updateWidgetPosition"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="6">
                <v-text-field
                  v-model.number="widgets[selectedWidget].top"
                  label="上边距"
                  type="number"
                  suffix="px"
                  @input="updateWidgetPosition"
                  density="compact"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row dense>
              <v-col cols="6">
                <v-text-field
                  v-model.number="widgets[selectedWidget].width"
                  label="宽度"
                  type="number"
                  suffix="px"
                  @input="updateWidgetSize"
                  density="compact"
                ></v-text-field>
              </v-col>
              <v-col cols="6">
                <v-text-field
                  v-model.number="widgets[selectedWidget].height"
                  label="高度"
                  type="number"
                  suffix="px"
                  @input="updateWidgetSize"
                  density="compact"
                ></v-text-field>
              </v-col>
            </v-row>
          </div>

          <!-- 屏幕信息 -->
          <div class="screen-info mt-4">
            <h4 class="mb-2">屏幕信息</h4>
            <p>分辨率: {{ screenSize.width }} × {{ screenSize.height }}</p>
          </div>
        </v-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { Canvas, Rect, FabricText, Line, Group } from 'fabric'
import { sendParams2Android } from '@/utils/androidMessage'

// 响应式数据
const canvasRef = ref(null)
let canvas = null
const selectedWidget = ref(null)

// 屏幕尺寸配置
const screenSize = reactive({
  width: 1920,
  height: 1080
})

// 画布配置
const canvasConfig = reactive({
  width: 800,
  height: 450,
  scale: 800 / 1920 // 缩放比例
})

// 组件数据
const widgets = reactive({
  antiCollision: {
    isShow: true,
    left: 100,
    top: 100,
    width: 200,
    height: 150
  },
  tooth: {
    isShow: true,
    left: 320,
    top: 100,
    width: 200,
    height: 150
  },
  posture: {
    isShow: true,
    left: 540,
    top: 100,
    width: 200,
    height: 150
  },
  handle: {
    isShow: true,
    left: 100,
    top: 270,
    width: 200,
    height: 150
  },
  deviceInformation: {
    isShow: true,
    left: 320,
    top: 270,
    width: 200,
    height: 150
  }
})

// 组件标签映射
const widgetLabels = {
  antiCollision: '防碰撞',
  tooth: '斗齿',
  posture: '姿态',
  handle: '手柄',
  deviceInformation: '设备信息'
}

// 获取组件标签
const getWidgetLabel = (key) => {
  return widgetLabels[key] || key
}

// 初始化画布
const initCanvas = () => {
  canvas = new Canvas('widgetCanvas', {
    width: canvasConfig.width,
    height: canvasConfig.height,
    backgroundColor: '#1a1a1a',
    selection: true
  })

  // 添加网格
  addGrid()

  // 渲染所有组件
  renderAllWidgets()

  // 监听选择事件
  canvas.on('selection:created', handleSelection)
  canvas.on('selection:updated', handleSelection)
  canvas.on('selection:cleared', () => {
    selectedWidget.value = null
  })

  // 监听对象移动和缩放事件
  canvas.on('object:moving', handleObjectMoving)
  canvas.on('object:scaling', handleObjectScaling)
  canvas.on('object:modified', handleObjectModified)
}

// 添加网格
const addGrid = () => {
  const gridSize = 20
  const gridLines = []

  // 垂直线
  for (let i = 0; i <= canvasConfig.width; i += gridSize) {
    const line = new Line([i, 0, i, canvasConfig.height], {
      stroke: '#424242',
      strokeWidth: 1,
      selectable: false,
      evented: false
    })
    gridLines.push(line)
  }

  // 水平线
  for (let i = 0; i <= canvasConfig.height; i += gridSize) {
    const line = new Line([0, i, canvasConfig.width, i], {
      stroke: '#424242',
      strokeWidth: 1,
      selectable: false,
      evented: false
    })
    gridLines.push(line)
  }

  // 添加到画布
  gridLines.forEach(line => canvas.add(line))
}

// 渲染所有组件
const renderAllWidgets = () => {
  Object.keys(widgets).forEach(key => {
    if (widgets[key].isShow) {
      createWidgetRect(key)
    }
  })
}

// 创建组件矩形
const createWidgetRect = (widgetKey) => {
  const widget = widgets[widgetKey]

  const rect = new Rect({
    left: widget.left * canvasConfig.scale,
    top: widget.top * canvasConfig.scale,
    width: widget.width * canvasConfig.scale,
    height: widget.height * canvasConfig.scale,
    fill: 'rgba(255, 95, 0, 0.3)',
    stroke: '#ff5f00',
    strokeWidth: 2,
    cornerColor: '#ff5f00',
    cornerSize: 8,
    transparentCorners: false,
    widgetKey: widgetKey
  })

  const text = new FabricText(getWidgetLabel(widgetKey), {
    left: widget.left * canvasConfig.scale + (widget.width * canvasConfig.scale) / 2,
    top: widget.top * canvasConfig.scale + (widget.height * canvasConfig.scale) / 2,
    fontSize: 14,
    fill: '#ffffff',
    textAlign: 'center',
    originX: 'center',
    originY: 'center',
    selectable: false,
    evented: false
  })

  const group = new Group([rect, text], {
    widgetKey: widgetKey,
    lockRotation: true
  })

  canvas.add(group)
}

// 处理选择事件
const handleSelection = (e) => {
  const activeObject = e.target
  if (activeObject && activeObject.widgetKey) {
    selectedWidget.value = activeObject.widgetKey
  }
}

// 处理对象移动
const handleObjectMoving = (e) => {
  const obj = e.target
  if (obj.widgetKey) {
    // 更新组件位置数据
    widgets[obj.widgetKey].left = Math.round(obj.left / canvasConfig.scale)
    widgets[obj.widgetKey].top = Math.round(obj.top / canvasConfig.scale)
  }
}

// 处理对象缩放
const handleObjectScaling = (e) => {
  const obj = e.target
  if (obj.widgetKey) {
    // 更新组件尺寸数据
    widgets[obj.widgetKey].width = Math.round(obj.getScaledWidth() / canvasConfig.scale)
    widgets[obj.widgetKey].height = Math.round(obj.getScaledHeight() / canvasConfig.scale)
  }
}

// 处理对象修改完成
const handleObjectModified = (e) => {
  const obj = e.target
  if (obj.widgetKey) {
    // 重置缩放，保持实际尺寸
    obj.set({
      scaleX: 1,
      scaleY: 1,
      width: widgets[obj.widgetKey].width * canvasConfig.scale,
      height: widgets[obj.widgetKey].height * canvasConfig.scale
    })
    canvas.renderAll()
  }
}

// 更新组件可见性
const updateWidgetVisibility = (widgetKey) => {
  // 移除现有对象
  const objects = canvas.getObjects().filter(obj => obj.widgetKey === widgetKey)
  objects.forEach(obj => canvas.remove(obj))

  // 如果显示，重新创建
  if (widgets[widgetKey].isShow) {
    createWidgetRect(widgetKey)
  }

  canvas.renderAll()
}

// 更新组件位置
const updateWidgetPosition = () => {
  if (!selectedWidget.value) return

  const widgetKey = selectedWidget.value
  const objects = canvas.getObjects().filter(obj => obj.widgetKey === widgetKey)

  objects.forEach(obj => {
    obj.set({
      left: widgets[widgetKey].left * canvasConfig.scale,
      top: widgets[widgetKey].top * canvasConfig.scale
    })
  })

  canvas.renderAll()
}

// 更新组件尺寸
const updateWidgetSize = () => {
  if (!selectedWidget.value) return

  const widgetKey = selectedWidget.value
  const objects = canvas.getObjects().filter(obj => obj.widgetKey === widgetKey)

  objects.forEach(obj => {
    if (obj.type === 'group') {
      const rect = obj.getObjects()[0]
      const text = obj.getObjects()[1]

      rect.set({
        width: widgets[widgetKey].width * canvasConfig.scale,
        height: widgets[widgetKey].height * canvasConfig.scale
      })

      text.set({
        left: (widgets[widgetKey].width * canvasConfig.scale) / 2,
        top: (widgets[widgetKey].height * canvasConfig.scale) / 2
      })
    }
  })

  canvas.renderAll()
}

// 应用设置
const handleApply = async () => {
  // try {
    const payload = {
      screen: {
        width: screenSize.width,
        height: screenSize.height
      },
      ...widgets
    }

    await sendParams2Android('controlPosition', JSON.parse(JSON.stringify(payload)))

    console.log('组件布局已应用:', payload)
  // } catch (error) {
  //   console.error('应用组件布局失败:', error)
  // }
}

// 取消
const handleCancel = () => {
  // 可以添加取消逻辑，比如恢复到初始状态
  console.log('取消组件布局调整')
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  initCanvas()
})

// 组件卸载
onBeforeUnmount(() => {
  if (canvas) {
    canvas.dispose()
  }
})
</script>

<style scoped>
.widget-layout-editor {
  height: calc(100vh - var(--app-bar-height));
  display: flex;
  flex-direction: column;
  background-color: #0e0e0e;
  color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #212121;
  border-bottom: 1px solid #424242;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #ff5f00;
}

.content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
}

.canvas-container {
  flex: 1;
  background-color: #212121;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  border: 1px solid #424242;
}

.canvas-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.control-panel {
  width: 300px;
  height: fit-content;
}

.widget-item {
  padding: 8px;
  border: 1px solid #424242;
  border-radius: 4px;
  background-color: #212121;
  margin-bottom: 8px;
}

.widget-properties {
  border-top: 1px solid #424242;
  padding-top: 16px;
}

.screen-info {
  border-top: 1px solid #424242;
  padding-top: 16px;
}

/* 深色主题下的文本颜色 */
.widget-item label,
.widget-properties h4,
.screen-info h4,
.screen-info p {
  color: #ffffff !important;
}

/* 画布背景调整为深色 */
#widgetCanvas {
  border: 1px solid #424242;
  border-radius: 4px;
}
</style>